import { MarkdownParser } from '../../../src/services/MarkdownParser';

// Mock the marked library
vi.mock('marked', () => {
  const mockRenderer = {
    code: vi.fn(),
    codespan: vi.fn(),
    image: vi.fn(),
    link: vi.fn(),
    blockquote: vi.fn(),
    table: vi.fn(),
  };

  const mockMarked = vi.fn();
  mockMarked.use = vi.fn();

  return {
    marked: mockMarked,
    Renderer: vi.fn().mockImplementation(() => mockRenderer),
  };
});

// Import after mocking
const mockMarked = vi.mocked((await import('marked')).marked);

describe('MarkdownParser', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('parse', () => {
    it('parses basic markdown correctly', () => {
      mockMarked.mockReturnValue('<h1>Hello World</h1><p>This is a paragraph.</p>');

      const result = MarkdownParser.parse('# Hello World\n\nThis is a paragraph.');

      expect(result.html).toContain('<h1');
      expect(result.html).toContain('Hello World');
      expect(result.html).toContain('<p>');
      expect(result.html).toContain('This is a paragraph.');
      expect(result.processingTime).toBeGreaterThan(0);
      expect(result.error).toBeUndefined();
    });

    it('handles empty content', () => {
      const result = MarkdownParser.parse('');
      
      expect(result.html).toContain('empty-preview');
      expect(result.html).toContain('Start editing to see preview');
      expect(result.error).toBeUndefined();
    });

    it('handles whitespace-only content', () => {
      const result = MarkdownParser.parse('   \n\t  \n  ');
      
      expect(result.html).toContain('empty-preview');
      expect(result.error).toBeUndefined();
    });

    it('calls marked with the provided content', () => {
      mockMarked.mockReturnValue('<p>Test output</p>');

      const result = MarkdownParser.parse('Test input');

      expect(mockMarked).toHaveBeenCalledWith('Test input');
      expect(result.html).toBe('<p>Test output</p>');
    });

    it('returns processing time', () => {
      mockMarked.mockReturnValue('<p>Test</p>');

      const result = MarkdownParser.parse('Test');

      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('handles parsing errors gracefully', () => {
      // Mock marked to throw an error
      mockMarked.mockImplementation(() => {
        throw new Error('Parse error');
      });

      const result = MarkdownParser.parse('# Test');

      expect(result.html).toContain('error-preview');
      expect(result.html).toContain('Markdown Parsing Error');
      expect(result.error).toBe('Parse error');
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('calls marked function with content', () => {
      mockMarked.mockReturnValue('<p>Test</p>');

      MarkdownParser.parse('Test content');

      expect(mockMarked).toHaveBeenCalledWith('Test content');
    });
  });

  describe('parseAsync', () => {
    it('returns a promise that resolves with parse result', async () => {
      // Reset the mock and set up fresh return value
      mockMarked.mockReset();
      mockMarked.mockReturnValue('<h1>Async Test</h1>');

      const result = await MarkdownParser.parseAsync('# Async Test');

      expect(result.html).toContain('<h1');
      expect(result.html).toContain('Async Test');
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('handles async parsing errors', async () => {
      // Mock marked to throw an error
      mockMarked.mockImplementation(() => {
        throw new Error('Async parse error');
      });

      const result = await MarkdownParser.parseAsync('# Test');

      expect(result.error).toBe('Async parse error');
      expect(result.html).toContain('error-preview');
    });
  });

  describe('isLargeContent', () => {
    it('returns false for small content', () => {
      expect(MarkdownParser.isLargeContent('Small content')).toBe(false);
    });

    it('returns true for large content', () => {
      const largeContent = 'x'.repeat(60000);
      expect(MarkdownParser.isLargeContent(largeContent)).toBe(true);
    });

    it('returns false for content at threshold', () => {
      const thresholdContent = 'x'.repeat(50000);
      expect(MarkdownParser.isLargeContent(thresholdContent)).toBe(false);
    });

    it('returns true for content above threshold', () => {
      const aboveThresholdContent = 'x'.repeat(50001);
      expect(MarkdownParser.isLargeContent(aboveThresholdContent)).toBe(true);
    });
  });

  describe('estimateProcessingTime', () => {
    it('returns minimum time for very small content', () => {
      expect(MarkdownParser.estimateProcessingTime('')).toBe(10);
      expect(MarkdownParser.estimateProcessingTime('small')).toBe(10);
    });

    it('estimates time based on content length', () => {
      const content1000 = 'x'.repeat(1000);
      const content5000 = 'x'.repeat(5000);
      
      expect(MarkdownParser.estimateProcessingTime(content1000)).toBe(10); // min 10ms
      expect(MarkdownParser.estimateProcessingTime(content5000)).toBe(10); // min 10ms
      
      const content20000 = 'x'.repeat(20000);
      expect(MarkdownParser.estimateProcessingTime(content20000)).toBe(20);
    });

    it('scales with content size', () => {
      const content100k = 'x'.repeat(100000);
      expect(MarkdownParser.estimateProcessingTime(content100k)).toBe(100);
    });
  });
});
